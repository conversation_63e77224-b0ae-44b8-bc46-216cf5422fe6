import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';
import {
  rateLimit,
  csrfProtection,
  addSecurityHeaders,
  validateRequest
} from './lib/security/middleware';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Apply security validations first
  const validationError = validateRequest(request);
  if (validationError) {
    return addSecurityHeaders(validationError);
  }

  // Apply rate limiting
  const rateLimitError = rateLimit(request);
  if (rateLimitError) {
    return addSecurityHeaders(rateLimitError);
  }

  // Apply CSRF protection for state-changing requests
  const csrfError = await csrfProtection(request);
  if (csrfError) {
    return addSecurityHeaders(csrfError);
  }

  // Get token for all protected routes
  const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET });

  // Check if the path is for the admin panel
  if (pathname.startsWith('/admin')) {
    console.log('Admin route accessed:', pathname);

    // Skip authentication check for admin login page and debug pages
    if (pathname === '/admin/login' || pathname === '/admin/check') {
      console.log('Allowing access to:', pathname);
      return NextResponse.next();
    }

    console.log('Token from middleware:', JSON.stringify(token, null, 2));

    // If user is not logged in
    if (!token) {
      console.log('No token found for admin route:', pathname);

      // For API routes, return JSON error instead of redirect
      if (pathname.startsWith('/api/admin')) {
        return NextResponse.json(
          { message: "Authentication required", code: "UNAUTHORIZED" },
          { status: 401 }
        );
      }

      // For regular admin pages, redirect to admin login
      const url = new URL('/admin/login', request.url);
      url.searchParams.set('callbackUrl', encodeURI(pathname));
      return NextResponse.redirect(url);
    }

    // Check user account status
    if (token.status !== 'active' || !token.isActive) {
      console.log('User account is not active:', {
        status: token.status,
        isActive: token.isActive,
        userId: token.id
      });
      // Clear session and redirect to login with error message
      const response = NextResponse.redirect(new URL('/admin/login?error=account_disabled', request.url));
      response.cookies.delete('next-auth.session-token');
      response.cookies.delete('__Secure-next-auth.session-token');
      return response;
    }

    // Check if user is an admin
    const isAdmin = token.isAdmin === true;
    console.log('Is admin check:', { isAdmin, tokenIsAdmin: token.isAdmin });

    if (!isAdmin) {
      console.log('User is not an admin:', pathname);

      // For API routes, return JSON error instead of redirect
      if (pathname.startsWith('/api/admin')) {
        return NextResponse.json(
          { message: "Admin access required", code: "FORBIDDEN" },
          { status: 403 }
        );
      }

      // For regular admin pages, redirect to home
      return NextResponse.redirect(new URL('/', request.url));
    }

    console.log('Admin access granted for:', pathname);
  }

  // Check user status for all other protected routes (non-public routes)
  const protectedRoutes = ['/dashboard', '/user', '/wallet', '/groups', '/api'];
  const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route));

  if (isProtectedRoute && token) {
    // Check user account status for all protected routes
    if (token.status !== 'active' || !token.isActive) {
      console.log('User account is not active on protected route:', {
        pathname,
        status: token.status,
        isActive: token.isActive,
        userId: token.id
      });

      // For API routes, return 401
      if (pathname.startsWith('/api')) {
        return NextResponse.json(
          {
            message: "Account disabled or suspended",
            code: "ACCOUNT_DISABLED",
            status: token.status
          },
          { status: 401 }
        );
      }

      // For regular routes, clear session and redirect to login
      const response = NextResponse.redirect(new URL('/login?error=account_disabled', request.url));
      response.cookies.delete('next-auth.session-token');
      response.cookies.delete('__Secure-next-auth.session-token');
      return response;
    }
  }

  // Add security headers to all responses
  const response = NextResponse.next();
  return addSecurityHeaders(response);
}

// Configure the middleware to run only on specific paths
export const config = {
  matcher: [
    '/admin/:path*',
    '/dashboard/:path*',
    '/user/:path*',
    '/wallet/:path*',
    '/groups/:path*',
    '/api/:path*',
  ],
};
