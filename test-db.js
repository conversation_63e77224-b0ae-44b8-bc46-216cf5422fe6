const mysql = require('mysql2/promise');

async function testConnection() {
  try {
    console.log('Testing database connection...');
    
    const connection = await mysql.createConnection({
      host: 'inpro2.fcomet.com',
      user: 'unlifyc2_hifnf_drizzle',
      password: 'Os5S6fqh@R7I',
      database: 'unlifyc2_hifnf_drizzle',
      port: 3306
    });

    console.log('✅ Database connection successful!');
    
    // Test a simple query
    const [rows] = await connection.execute('SELECT 1 as test');
    console.log('✅ Query test successful:', rows);
    
    // Test payment_gateways table
    try {
      const [gateways] = await connection.execute('SELECT COUNT(*) as count FROM payment_gateways');
      console.log('✅ Payment gateways table accessible:', gateways);
    } catch (tableError) {
      console.error('❌ Payment gateways table error:', tableError.message);
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.error('Error code:', error.code);
    console.error('Error errno:', error.errno);
  }
}

testConnection();
