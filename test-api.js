// Using built-in fetch in Node.js 18+

async function testAPI() {
  try {
    console.log('Testing API endpoint...');
    
    const response = await fetch('http://localhost:3001/api/admin/payment-gateways');
    
    console.log('Status:', response.status);
    console.log('Status Text:', response.statusText);
    console.log('Headers:', Object.fromEntries(response.headers.entries()));
    
    const text = await response.text();
    console.log('Response Body:');
    console.log(text);
    
    // Try to parse as JSON
    try {
      const json = JSON.parse(text);
      console.log('Parsed JSON:', json);
    } catch (parseError) {
      console.log('Failed to parse as JSON:', parseError.message);
      console.log('First 200 characters:', text.substring(0, 200));
    }
    
  } catch (error) {
    console.error('Request failed:', error.message);
  }
}

testAPI();
