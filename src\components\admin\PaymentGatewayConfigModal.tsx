"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Dialog, DialogPanel, DialogTitle } from "@headlessui/react";
import { XMarkIcon, CreditCardIcon, BanknotesIcon, Cog6ToothIcon } from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { paymentGatewaySchema, type PaymentGatewayFormData } from "@/lib/wallet/validation";
import { toast } from "react-hot-toast";

interface PaymentGateway {
  id: string;
  name: string;
  displayName: string;
  type: string;
  isActive: boolean;
  config: any;
  depositFee: string;
  depositFixedFee: string;
  minDeposit: string;
  maxDeposit: string;
  currency: string;
  sortOrder: number;
}

interface PaymentGatewayConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  gateway?: PaymentGateway | null;
  mode: 'create' | 'edit';
}

const gatewayTypes = [
  { value: 'manual', label: 'Manual Payment Method', icon: BanknotesIcon, description: 'Manual payment processing with instructions' },
  { value: 'uddoktapay', label: 'UddoktaPay', icon: Cog6ToothIcon, description: 'UddoktaPay payment gateway' },
];

export function PaymentGatewayConfigModal({ isOpen, onClose, onSuccess, gateway, mode }: PaymentGatewayConfigModalProps) {
  const [loading, setLoading] = useState(false);
  const [selectedType, setSelectedType] = useState<string>('manual');

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<PaymentGatewayFormData>({
    resolver: zodResolver(paymentGatewaySchema),
    defaultValues: {
      type: 'manual',
      isActive: false,
      depositFee: '0.00',
      depositFixedFee: '0.00',
      minDeposit: '1.00',
      maxDeposit: '10000.00',
      currency: 'USD',
      sortOrder: 0,
      config: {},
    },
  });

  const watchedType = watch("type");

  useEffect(() => {
    setSelectedType(watchedType);
  }, [watchedType]);

  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && gateway) {
        // Populate form with gateway data
        reset({
          name: gateway.name,
          displayName: gateway.displayName,
          type: gateway.type as any,
          isActive: gateway.isActive,
          config: gateway.config || {},
          depositFee: gateway.depositFee,
          depositFixedFee: gateway.depositFixedFee,
          minDeposit: gateway.minDeposit,
          maxDeposit: gateway.maxDeposit,
          currency: gateway.currency,
          sortOrder: gateway.sortOrder,
        });
        setSelectedType(gateway.type);
      } else {
        // Reset form for create mode
        reset({
          type: 'manual',
          isActive: false,
          depositFee: '0.00',
          depositFixedFee: '0.00',
          minDeposit: '1.00',
          maxDeposit: '10000.00',
          currency: 'USD',
          sortOrder: 0,
          config: {},
        });
        setSelectedType('manual');
      }
    }
  }, [isOpen, mode, gateway, reset]);

  const onSubmit = async (data: PaymentGatewayFormData) => {
    setLoading(true);
    try {
      const url = mode === 'edit' && gateway 
        ? `/api/admin/payment-gateways/${gateway.id}`
        : '/api/admin/payment-gateways';
      
      const method = mode === 'edit' ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();
      
      if (result.success) {
        toast.success(result.message);
        onSuccess();
      } else {
        toast.error(result.message || 'Failed to save gateway');
        if (result.errors) {
          result.errors.forEach((error: string) => toast.error(error));
        }
      }
    } catch (error) {
      console.error('Error saving gateway:', error);
      toast.error('Failed to save gateway');
    } finally {
      setLoading(false);
    }
  };

  const renderConfigFields = () => {
    switch (selectedType) {

      case 'uddoktapay':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                API Key *
              </label>
              <Input
                {...register("config.apiKey")}
                type="password"
                placeholder="UddoktaPay API Key"
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                API URL *
              </label>
              <Input
                {...register("config.apiUrl")}
                placeholder="https://sandbox.uddoktapay.com/api/checkout-v2"
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Store ID *
              </label>
              <Input
                {...register("config.storeId")}
                placeholder="Store ID"
                className="w-full"
              />
            </div>
          </div>
        );

      case 'manual':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Payment Instructions *
              </label>
              <textarea
                {...register("config.instructions")}
                rows={4}
                placeholder="Enter payment instructions for users..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">
                Instructions that will be shown to users when they select this payment method
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Account Details
              </label>
              <textarea
                {...register("config.accountDetails")}
                rows={3}
                placeholder="Bank account or payment details..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">
                Account details that users need for making payments
              </p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <DialogPanel className="mx-auto max-w-2xl w-full bg-white rounded-lg shadow-xl max-h-[90vh] flex flex-col">
          <div className="flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0">
            <DialogTitle className="text-lg font-medium text-gray-900">
              {mode === 'edit' ? 'Edit Payment Gateway' : 'Add Payment Gateway'}
            </DialogTitle>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col flex-1 min-h-0">
            <div className="p-6 space-y-6 overflow-y-auto flex-1">
            {/* Gateway Type Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Gateway Type *
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {gatewayTypes.map((type) => {
                  const Icon = type.icon;
                  return (
                    <label
                      key={type.value}
                      className={`relative flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                        selectedType === type.value
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-300'
                      }`}
                    >
                      <input
                        type="radio"
                        {...register("type")}
                        value={type.value}
                        className="sr-only"
                      />
                      <Icon className="h-5 w-5 text-gray-600 mr-3" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {type.label}
                        </div>
                        <div className="text-xs text-gray-500">
                          {type.description}
                        </div>
                      </div>
                    </label>
                  );
                })}
              </div>
              {errors.type && (
                <p className="text-red-600 text-sm mt-1">{errors.type.message}</p>
              )}
            </div>

            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Gateway Name *
                </label>
                <Input
                  {...register("name")}
                  placeholder="e.g., manual_bank"
                  className="w-full"
                />
                {errors.name && (
                  <p className="text-red-600 text-sm mt-1">{errors.name.message}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Display Name *
                </label>
                <Input
                  {...register("displayName")}
                  placeholder="e.g., Credit/Debit Card"
                  className="w-full"
                />
                {errors.displayName && (
                  <p className="text-red-600 text-sm mt-1">{errors.displayName.message}</p>
                )}
              </div>
            </div>

            {/* Gateway Configuration */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Gateway Configuration
              </h3>
              {renderConfigFields()}
            </div>

            {/* Fee Configuration */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Fee Configuration
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Deposit Fee (%)
                  </label>
                  <Input
                    {...register("depositFee")}
                    type="number"
                    step="0.01"
                    min="0"
                    max="10"
                    placeholder="0.00"
                    className="w-full"
                  />
                  {errors.depositFee && (
                    <p className="text-red-600 text-sm mt-1">{errors.depositFee.message}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Fixed Fee ($)
                  </label>
                  <Input
                    {...register("depositFixedFee")}
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="0.00"
                    className="w-full"
                  />
                  {errors.depositFixedFee && (
                    <p className="text-red-600 text-sm mt-1">{errors.depositFixedFee.message}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Limits Configuration */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Transaction Limits
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Min Deposit ($)
                  </label>
                  <Input
                    {...register("minDeposit")}
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="1.00"
                    className="w-full"
                  />
                  {errors.minDeposit && (
                    <p className="text-red-600 text-sm mt-1">{errors.minDeposit.message}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Max Deposit ($)
                  </label>
                  <Input
                    {...register("maxDeposit")}
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="10000.00"
                    className="w-full"
                  />
                  {errors.maxDeposit && (
                    <p className="text-red-600 text-sm mt-1">{errors.maxDeposit.message}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Currency
                  </label>
                  <select
                    {...register("currency")}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="GBP">GBP</option>
                    <option value="BDT">BDT</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Additional Settings */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Sort Order
                </label>
                <Input
                  {...register("sortOrder")}
                  type="number"
                  min="0"
                  placeholder="0"
                  className="w-full"
                />
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  {...register("isActive")}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-sm text-gray-900">
                  Enable this gateway
                </label>
              </div>
            </div>

            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 flex-shrink-0 bg-white">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loading}
                className="flex items-center space-x-2"
              >
                {loading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                )}
                <span>{mode === 'edit' ? 'Update Gateway' : 'Create Gateway'}</span>
              </Button>
            </div>
          </form>
        </DialogPanel>
      </div>
    </Dialog>
  );
}
