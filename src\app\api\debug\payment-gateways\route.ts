import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { PaymentGatewayService } from "@/lib/payment/paymentGatewayService";
import { db } from "@/lib/db";
import { paymentGateways } from "@/lib/db/schema";

export async function GET() {
  try {
    // Skip auth check for debugging
    console.log('Debug endpoint accessed');

    // Test database connection first
    try {
      const testQuery = await db.query.paymentGateways.findMany({ limit: 1 });
      console.log('Database connection test successful:', testQuery.length);
    } catch (dbError) {
      console.error('Database connection failed:', dbError);
      return NextResponse.json({
        success: false,
        error: 'Database connection failed',
        details: dbError instanceof Error ? dbError.message : 'Unknown database error'
      }, { status: 500 });
    }

    // Get all payment gateways
    const allGateways = await PaymentGatewayService.getAllPaymentGateways();
    const activeGateways = await PaymentGatewayService.getActivePaymentGateways();

    // Validate each gateway configuration
    const gatewayValidation = allGateways.map(gateway => {
      const validation = PaymentGatewayService.validateGatewayConfig(
        gateway.type as any,
        gateway.config
      );

      return {
        id: gateway.id,
        name: gateway.name,
        displayName: gateway.displayName,
        type: gateway.type,
        isActive: gateway.isActive,
        isValid: validation.isValid,
        errors: validation.errors,
        config: {
          hasPublishableKey: !!gateway.config?.publishableKey,
          hasSecretKey: !!gateway.config?.secretKey,
          hasWebhookSecret: !!gateway.config?.webhookSecret,
          hasClientId: !!gateway.config?.clientId,
          hasClientSecret: !!gateway.config?.clientSecret,
          hasApiKey: !!gateway.config?.apiKey,
          hasInstructions: !!gateway.config?.instructions,
          mode: gateway.config?.mode,
        }
      };
    });

    return NextResponse.json({
      success: true,
      data: {
        totalGateways: allGateways.length,
        activeGateways: activeGateways.length,
        gateways: gatewayValidation,
      },
    });
  } catch (error: any) {
    console.error("Error debugging payment gateways:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to debug payment gateways",
        error: error.message
      },
      { status: 500 }
    );
  }
}
