import Stripe from 'stripe';

// Server-side Stripe configuration - only initialize if key is available
export const stripe = process.env.STRIPE_SECRET_KEY
  ? new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2024-12-18.acacia',
      typescript: true,
    })
  : null;

// Stripe configuration interface
export interface StripeConfig {
  publishableKey: string;
  secretKey: string;
  webhookSecret: string;
  mode: 'test' | 'live';
}

// Validate Stripe configuration
export function validateStripeConfig(config: StripeConfig): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!config.publishableKey) {
    errors.push('Publishable key is required');
  } else if (!config.publishableKey.startsWith('pk_')) {
    errors.push('Invalid publishable key format');
  }

  if (!config.secretKey) {
    errors.push('Secret key is required');
  } else if (!config.secretKey.startsWith('sk_')) {
    errors.push('Invalid secret key format');
  }

  if (!config.webhookSecret) {
    errors.push('Webhook secret is required');
  } else if (!config.webhookSecret.startsWith('whsec_')) {
    errors.push('Invalid webhook secret format');
  }

  if (!config.mode || !['test', 'live'].includes(config.mode)) {
    errors.push('Mode must be either "test" or "live"');
  }

  // Validate key consistency (test keys should be used with test mode, live keys with live mode)
  if (config.publishableKey && config.secretKey) {
    const isTestPublishable = config.publishableKey.includes('_test_');
    const isTestSecret = config.secretKey.includes('_test_');
    const isTestMode = config.mode === 'test';

    if (isTestMode && (!isTestPublishable || !isTestSecret)) {
      errors.push('Test mode requires test keys (keys should contain "_test_")');
    } else if (!isTestMode && (isTestPublishable || isTestSecret)) {
      errors.push('Live mode requires live keys (keys should not contain "_test_")');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// Create Stripe instance with custom configuration
export function createStripeInstance(config: StripeConfig): Stripe {
  return new Stripe(config.secretKey, {
    apiVersion: '2024-12-18.acacia',
    typescript: true,
  });
}

// Get public Stripe configuration for client-side
export function getPublicStripeConfig(): { publishableKey: string } {
  return {
    publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || ''
  };
}

// Stripe webhook signature verification
export function verifyWebhookSignature(
  payload: string | Buffer,
  signature: string,
  webhookSecret: string
): Stripe.Event | null {
  if (!stripe) {
    console.error('Stripe not initialized - webhook verification failed');
    return null;
  }

  try {
    return stripe.webhooks.constructEvent(payload, signature, webhookSecret);
  } catch (error) {
    console.error('Stripe webhook signature verification failed:', error);
    return null;
  }
}

// Common Stripe error handling
export function handleStripeError(error: any): { message: string; code?: string } {
  if (error.type === 'StripeCardError') {
    return {
      message: error.message || 'Your card was declined.',
      code: error.code
    };
  } else if (error.type === 'StripeRateLimitError') {
    return {
      message: 'Too many requests made to the API too quickly.',
      code: 'rate_limit'
    };
  } else if (error.type === 'StripeInvalidRequestError') {
    return {
      message: 'Invalid parameters were supplied to Stripe\'s API.',
      code: 'invalid_request'
    };
  } else if (error.type === 'StripeAPIError') {
    return {
      message: 'An error occurred with Stripe\'s API.',
      code: 'api_error'
    };
  } else if (error.type === 'StripeConnectionError') {
    return {
      message: 'A network error occurred.',
      code: 'connection_error'
    };
  } else if (error.type === 'StripeAuthenticationError') {
    return {
      message: 'Authentication with Stripe\'s API failed.',
      code: 'authentication_error'
    };
  } else {
    return {
      message: error.message || 'An unexpected error occurred.',
      code: 'unknown_error'
    };
  }
}
